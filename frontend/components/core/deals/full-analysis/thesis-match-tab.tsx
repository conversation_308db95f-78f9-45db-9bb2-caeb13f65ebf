"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Shield, 
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Sparkles,
  Target,
  FileText,
  ToggleLeft,
  ToggleRight,
  ChevronDown,
  ChevronUp,
  Hash,
  Calendar,
  Users,
  Building,
  DollarSign,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, ThesisScoring, QuestionScore } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface ThesisMatchTabProps {
  deal: DealDetailData
  fullAnalysisData?: any
}

const getQuestionTypeIcon = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return FileText
    case 'multi_select':
    case 'single_select':
      return ToggleLeft
    case 'boolean':
      return ToggleRight
    case 'number':
      return Hash
    case 'date':
      return Calendar
    default:
      return Target
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return 'bg-orange-100 text-orange-700 border-orange-200'
    case 'multi_select':
    case 'single_select':
      return 'bg-blue-100 text-blue-700 border-blue-200'
    case 'boolean':
      return 'bg-purple-100 text-purple-700 border-purple-200'
    case 'number':
      return 'bg-green-100 text-green-700 border-green-200'
    case 'date':
      return 'bg-pink-100 text-pink-700 border-pink-200'
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
  if (score >= 0.5) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  return 'text-red-600 bg-red-50 border-red-200'
}

const getScoreIcon = (score: number) => {
  if (score >= 0.8) return CheckCircle
  if (score >= 0.5) return AlertTriangle
  return XCircle
}

export function ThesisMatchTab({ deal, fullAnalysisData }: ThesisMatchTabProps) {
  const [showAllQuestions, setShowAllQuestions] = useState(false)
  const thesisData = deal.comprehensive_scoring?.thesis
  const exclusionResult = deal.exclusion_filter_result

  if (!thesisData && !exclusionResult) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="shield" />
        <EmptyPlaceholder.Title>No Thesis Analysis Available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been analyzed against any investment thesis yet. 
          Analysis will appear here once thesis matching is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const formatScore = (score: number) => {
    return `${(score * 100).toFixed(1)}%`
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getMatchStatusBadge = (score: number) => {
    if (score >= 0.8) return { text: 'Strong Match', color: 'bg-green-100 text-green-800 border-green-200' }
    if (score >= 0.6) return { text: 'Good Match', color: 'bg-blue-100 text-blue-800 border-blue-200' }
    if (score >= 0.4) return { text: 'Partial Match', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    return { text: 'Weak Match', color: 'bg-red-100 text-red-800 border-red-200' }
  }

  return (
    <div className="space-y-8">
      {/* Exclusion Filter Results */}
      {exclusionResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={cn(
            "border-2",
            exclusionResult.excluded 
              ? "border-red-200 bg-red-50/50" 
              : "border-green-200 bg-green-50/50"
          )}>
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                {exclusionResult.excluded ? (
                  <XCircle className="h-6 w-6 text-red-600" />
                ) : (
                  <CheckCircle className="h-6 w-6 text-green-600" />
                )}
                <div>
                  <CardTitle className={cn(
                    "text-lg",
                    exclusionResult.excluded ? "text-red-900" : "text-green-900"
                  )}>
                    {exclusionResult.excluded ? "Excluded by Filter" : "Passed Exclusion Filters"}
                  </CardTitle>
                  {exclusionResult.reason && (
                    <p className={cn(
                      "text-sm mt-1",
                      exclusionResult.excluded ? "text-red-700" : "text-green-700"
                    )}>
                      {exclusionResult.reason}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
        </motion.div>
      )}

      {/* Thesis Match Results */}
      {thesisData && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="space-y-6"
        >
          {/* Thesis Header */}
          <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader className="pb-6">
              <div className="flex items-start justify-between">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Shield className="h-8 w-8 text-blue-600" />
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        {thesisData.thesis_name || 'Investment Thesis'}
                      </h2>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          Matched to {thesisData.thesis_name || 'Thesis'}
                        </Badge>
                        <Badge className={getMatchStatusBadge(thesisData.normalized_score).color}>
                          {getMatchStatusBadge(thesisData.normalized_score).text}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      <span>Score: {formatScore(thesisData.normalized_score)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>{Object.keys(thesisData.question_scores || {}).length} Questions</span>
                    </div>
                    {deal.comprehensive_scoring?.metadata?.scored_at && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Last Updated: {formatDate(deal.comprehensive_scoring.metadata.scored_at)}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-right space-y-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className={cn(
                          "text-4xl font-bold px-4 py-2 rounded-xl border-2 cursor-help",
                          getScoreColor(thesisData.normalized_score)
                        )}>
                          {formatScore(thesisData.normalized_score)}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Normalized score calculated from {Object.keys(thesisData.question_scores || {}).length} thesis criteria.
                          Score reflects weighted average of all matching rules.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Link href={`/theses/${thesisData.thesis_id}`}>
                    <Button variant="outline" size="sm" className="gap-2">
                      Go to Thesis
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Question Breakdown */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-600" />
                Question-by-Question Breakdown
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Detailed analysis of how this deal matches against each thesis criterion
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {(() => {
                const questions = Object.entries(thesisData.question_scores || {})
                const shouldCollapse = questions.length > 5
                const questionsToShow = shouldCollapse && !showAllQuestions
                  ? questions.slice(0, 5)
                  : questions

                return (
                  <>
                    {questionsToShow.map(([questionId, scoreData], index) => {
                const Icon = getQuestionTypeIcon(scoreData.question_type)
                const ScoreIcon = getScoreIcon(scoreData.raw_score)
                
                return (
                  <motion.div
                    key={questionId}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <Card className="border border-gray-200 hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          {/* Question Header */}
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              <div className={cn(
                                "p-2 rounded-lg border",
                                getQuestionTypeColor(scoreData.question_type)
                              )}>
                                <Icon className="h-4 w-4" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-semibold text-lg text-gray-900">
                                  Question {questionId}
                                </h4>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {scoreData.question_type.replace('_', ' ')}
                                  </Badge>
                                  <Badge variant="secondary" className="text-xs">
                                    Weight: {scoreData.weight}
                                  </Badge>
                                  {scoreData.ai_generated && (
                                    <Badge className="text-xs bg-purple-100 text-purple-700 border-purple-200">
                                      <Zap className="h-3 w-3 mr-1" />
                                      AI
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                              <div className={cn(
                                "flex items-center gap-2 px-3 py-2 rounded-lg border font-semibold",
                                getScoreColor(scoreData.raw_score)
                              )}>
                                <ScoreIcon className="h-4 w-4" />
                                {formatScore(scoreData.raw_score)}
                              </div>
                            </div>
                          </div>

                          {/* Explanation */}
                          <div className="bg-gray-50 rounded-lg p-4">
                            <p className="text-sm text-gray-700 leading-relaxed">
                              {scoreData.explanation || 'No explanation available'}
                            </p>
                          </div>

                          {/* Score Details */}
                          <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                            <span>Weighted Score: {scoreData.weighted_score.toFixed(2)}</span>
                            {scoreData.aggregation_used && (
                              <span>Aggregation: {scoreData.aggregation_type}</span>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    </motion.div>
                  )
                })}

                {/* Show More/Less Button */}
                {shouldCollapse && (
                  <div className="flex justify-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowAllQuestions(!showAllQuestions)}
                      className="gap-2"
                    >
                      {showAllQuestions ? (
                        <>
                          <ChevronUp className="h-4 w-4" />
                          Show Less ({questions.length - 5} hidden)
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-4 w-4" />
                          Show All Questions ({questions.length - 5} more)
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </>
            )
          })()}
            </CardContent>
          </Card>

          {/* Bonus Scores */}
          {thesisData.bonus_scores && Object.keys(thesisData.bonus_scores).length > 0 && (
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  Bonus Points
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(thesisData.bonus_scores).map(([ruleId, bonusData]) => (
                  <div key={ruleId} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <span className="text-sm font-medium text-green-900">
                      {bonusData.explanation}
                    </span>
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      +{bonusData.bonus_points} pts
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </motion.div>
      )}
    </div>
  )
}
