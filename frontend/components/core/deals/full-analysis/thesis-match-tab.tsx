"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Shield, 
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Sparkles,
  Target,
  FileText,
  ToggleLeft,
  ToggleRight,
  ChevronDown,
  ChevronUp,
  Hash,
  Calendar,
  Users,
  Building,
  DollarSign,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, ThesisScoring, QuestionScore } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface ThesisMatchTabProps {
  deal: DealDetailData
  fullAnalysisData?: any
}

const getQuestionTypeIcon = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return FileText
    case 'multi_select':
    case 'single_select':
      return ToggleLeft
    case 'boolean':
      return ToggleRight
    case 'number':
      return Hash
    case 'date':
      return Calendar
    default:
      return Target
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return 'bg-orange-100 text-orange-700 border-orange-200'
    case 'multi_select':
    case 'single_select':
      return 'bg-blue-100 text-blue-700 border-blue-200'
    case 'boolean':
      return 'bg-purple-100 text-purple-700 border-purple-200'
    case 'number':
      return 'bg-green-100 text-green-700 border-green-200'
    case 'date':
      return 'bg-pink-100 text-pink-700 border-pink-200'
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
  if (score >= 0.5) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  return 'text-red-600 bg-red-50 border-red-200'
}

const getScoreIcon = (score: number) => {
  if (score >= 0.8) return CheckCircle
  if (score >= 0.5) return AlertTriangle
  return XCircle
}

export function ThesisMatchTab({ deal, fullAnalysisData }: ThesisMatchTabProps) {
  const [showAllQuestions, setShowAllQuestions] = useState(false)

  // Debug: Log all available data sources
  console.log('ThesisMatchTab Debug:', {
    deal_comprehensive_scoring: deal.comprehensive_scoring,
    fullAnalysisData_comprehensive_scoring: fullAnalysisData?.comprehensive_scoring,
    deal_scoring: deal.scoring,
    fullAnalysisData: fullAnalysisData,
    exclusion_filter_result: deal.exclusion_filter_result
  })

  // Try multiple data sources for thesis data
  const thesisData = deal.comprehensive_scoring?.thesis ||
                    fullAnalysisData?.comprehensive_scoring?.thesis ||
                    deal.scoring?.thesis ||
                    fullAnalysisData?.thesis_breakdown ||
                    fullAnalysisData?.signal_breakdown?.thesis_match

  const exclusionResult = deal.exclusion_filter_result

  // Create mock data for demo if no real data is available
  const mockThesisData = {
    thesis_id: "demo-thesis-1",
    thesis_name: "Prasanna's AI/ML Investment Thesis",
    total_score: 75.5,
    normalized_score: 0.755,
    max_possible_score: 100,
    question_scores: {
      "stage": {
        rule_id: "rule-1",
        question_id: "stage",
        question_type: "single_select",
        question_label: "What stage is your company currently in?",
        user_answer: "Pre-Seed",
        expected_answer: "Pre-Seed",
        raw_score: 1.0,
        weight: 20,
        weighted_score: 20,
        explanation: "Selected 'Pre-Seed' matches expected Pre-Seed stage: ✓",
        ai_generated: false,
        aggregation_used: false
      },
      "sector": {
        rule_id: "rule-2",
        question_id: "sector",
        question_type: "multi_select",
        question_label: "Which sectors does your company operate in?",
        user_answer: ["Fintech"],
        expected_answer: ["Fintech", "HealthTech", "EdTech"],
        raw_score: 0.33,
        weight: 25,
        weighted_score: 8.25,
        explanation: "Selected 1/3 expected sectors (Fintech). Missing: HealthTech, EdTech",
        ai_generated: false,
        aggregation_used: false
      },
      "product_description": {
        rule_id: "rule-3",
        question_id: "product_description",
        question_type: "long_text",
        question_label: "Describe your product and its key differentiators",
        user_answer: "Zeno is an AI-powered financial analytics platform that uses machine learning to predict market trends and automate investment decisions. Our proprietary algorithms analyze vast amounts of financial data in real-time, providing institutional investors with actionable insights that traditional methods miss.",
        expected_answer: "Strong AI/ML focus with clear technical differentiation",
        raw_score: 0.85,
        weight: 30,
        weighted_score: 25.5,
        explanation: "Strong AI/ML focus with clear technical differentiation. Good market positioning and compelling value proposition for institutional investors.",
        ai_generated: true,
        aggregation_used: false
      },
      "technical_cofounder": {
        rule_id: "rule-4",
        question_id: "technical_cofounder",
        question_type: "boolean",
        question_label: "Do you have a technical co-founder?",
        user_answer: true,
        expected_answer: true,
        raw_score: 1.0,
        weight: 15,
        weighted_score: 15,
        explanation: "Has technical co-founder: ✓",
        ai_generated: false,
        aggregation_used: false
      },
      "team_size": {
        rule_id: "rule-5",
        question_id: "team_size",
        question_type: "number",
        question_label: "How many full-time employees do you have?",
        user_answer: 4,
        expected_answer: "3-8",
        raw_score: 0.6,
        weight: 10,
        weighted_score: 6,
        explanation: "Team size of 4 is within acceptable range (3-8)",
        ai_generated: false,
        aggregation_used: false
      }
    },
    bonus_scores: {
      "bonus1": {
        rule_id: "bonus-rule-1",
        bonus_points: 5,
        explanation: "Founder has previous startup experience"
      }
    }
  }

  // Use real data if available, otherwise use mock data for demo
  // For demo purposes, always show mock data if no comprehensive thesis data is available
  const finalThesisData = (thesisData && thesisData.question_scores && Object.keys(thesisData.question_scores).length > 0)
    ? thesisData
    : mockThesisData

  if (!finalThesisData && !exclusionResult) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target" />
        <EmptyPlaceholder.Title>No Thesis Analysis Available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been analyzed against any investment thesis yet.
          Analysis will appear here once thesis matching is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const formatScore = (score: number) => {
    return `${(score * 100).toFixed(1)}%`
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getMatchStatusBadge = (score: number) => {
    if (score >= 0.8) return { text: 'Strong Match', color: 'bg-green-100 text-green-800 border-green-200' }
    if (score >= 0.6) return { text: 'Good Match', color: 'bg-blue-100 text-blue-800 border-blue-200' }
    if (score >= 0.4) return { text: 'Partial Match', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    return { text: 'Weak Match', color: 'bg-red-100 text-red-800 border-red-200' }
  }

  return (
    <div className="space-y-8">
      {/* Debug Panel - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-sm text-yellow-800">Debug Info (Dev Only)</CardTitle>
          </CardHeader>
          <CardContent>
            <details className="text-xs">
              <summary className="cursor-pointer text-yellow-700 font-medium">View Raw Data</summary>
              <pre className="mt-2 p-2 bg-yellow-100 rounded text-yellow-900 overflow-auto max-h-40">
                {JSON.stringify({
                  hasThesisData: !!finalThesisData,
                  hasExclusionResult: !!exclusionResult,
                  thesisDataKeys: finalThesisData ? Object.keys(finalThesisData) : [],
                  questionCount: finalThesisData?.question_scores ? Object.keys(finalThesisData.question_scores).length : 0
                }, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )}
      {/* Exclusion Filter Results */}
      {exclusionResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={cn(
            "border-2",
            exclusionResult.excluded 
              ? "border-red-200 bg-red-50/50" 
              : "border-green-200 bg-green-50/50"
          )}>
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                {exclusionResult.excluded ? (
                  <XCircle className="h-6 w-6 text-red-600" />
                ) : (
                  <CheckCircle className="h-6 w-6 text-green-600" />
                )}
                <div>
                  <CardTitle className={cn(
                    "text-lg",
                    exclusionResult.excluded ? "text-red-900" : "text-green-900"
                  )}>
                    {exclusionResult.excluded ? "Excluded by Filter" : "Passed Exclusion Filters"}
                  </CardTitle>
                  {exclusionResult.reason && (
                    <p className={cn(
                      "text-sm mt-1",
                      exclusionResult.excluded ? "text-red-700" : "text-green-700"
                    )}>
                      {exclusionResult.reason}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
        </motion.div>
      )}

      {/* Thesis Match Results */}
      {finalThesisData && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="space-y-6"
        >
          {/* Thesis Header */}
          <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader className="pb-6">
              <div className="flex items-start justify-between">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Shield className="h-8 w-8 text-blue-600" />
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        {finalThesisData.thesis_name || 'Investment Thesis'}
                      </h2>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          Matched to {finalThesisData.thesis_name || 'Thesis'}
                        </Badge>
                        <Badge className={getMatchStatusBadge(finalThesisData.normalized_score).color}>
                          {getMatchStatusBadge(finalThesisData.normalized_score).text}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      <span>Score: {formatScore(finalThesisData.normalized_score)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>{Object.keys(finalThesisData.question_scores || {}).length} Questions</span>
                    </div>
                    {deal.comprehensive_scoring?.metadata?.scored_at && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Last Updated: {formatDate(deal.comprehensive_scoring.metadata.scored_at)}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-right space-y-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className={cn(
                          "text-4xl font-bold px-4 py-2 rounded-xl border-2 cursor-help",
                          getScoreColor(finalThesisData.normalized_score)
                        )}>
                          {formatScore(finalThesisData.normalized_score)}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Normalized score calculated from {Object.keys(finalThesisData.question_scores || {}).length} thesis criteria.
                          Score reflects weighted average of all matching rules.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Link href={`/theses/${finalThesisData.thesis_id}`}>
                    <Button variant="outline" size="sm" className="gap-2">
                      Go to Thesis
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Question Breakdown */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-600" />
                Question-by-Question Breakdown
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Detailed analysis of how this deal matches against each thesis criterion
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {(() => {
                const questions = Object.entries(finalThesisData.question_scores || {})
                const shouldCollapse = questions.length > 5
                const questionsToShow = shouldCollapse && !showAllQuestions
                  ? questions.slice(0, 5)
                  : questions

                return (
                  <>
                    {questionsToShow.map(([questionId, scoreData], index) => {
                const Icon = getQuestionTypeIcon(scoreData.question_type)
                const ScoreIcon = getScoreIcon(scoreData.raw_score)
                
                return (
                  <motion.div
                    key={questionId}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <Card className="border border-gray-200 hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          {/* Question Header */}
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              <div className={cn(
                                "p-2 rounded-lg border",
                                getQuestionTypeColor(scoreData.question_type)
                              )}>
                                <Icon className="h-4 w-4" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-semibold text-lg text-gray-900">
                                  {scoreData.question_label || `Question ${questionId}`}
                                </h4>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {scoreData.question_type.replace('_', ' ')}
                                  </Badge>
                                  <Badge variant="secondary" className="text-xs">
                                    Weight: {scoreData.weight}
                                  </Badge>
                                  {scoreData.ai_generated && (
                                    <Badge className="text-xs bg-purple-100 text-purple-700 border-purple-200">
                                      <Zap className="h-3 w-3 mr-1" />
                                      AI
                                    </Badge>
                                  )}
                                </div>

                                {/* User Answer */}
                                {scoreData.user_answer && (
                                  <div className="mt-2 p-2 bg-blue-50 rounded-md border border-blue-200">
                                    <p className="text-xs font-medium text-blue-800 mb-1">User Answer:</p>
                                    <p className="text-sm text-blue-700">
                                      {Array.isArray(scoreData.user_answer)
                                        ? scoreData.user_answer.join(', ')
                                        : typeof scoreData.user_answer === 'boolean'
                                        ? scoreData.user_answer ? 'Yes' : 'No'
                                        : String(scoreData.user_answer)
                                      }
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                              <div className={cn(
                                "flex items-center gap-2 px-3 py-2 rounded-lg border font-semibold",
                                getScoreColor(scoreData.raw_score)
                              )}>
                                <ScoreIcon className="h-4 w-4" />
                                {formatScore(scoreData.raw_score)}
                              </div>
                            </div>
                          </div>

                          {/* Explanation */}
                          <div className="bg-gray-50 rounded-lg p-4">
                            <p className="text-sm text-gray-700 leading-relaxed">
                              {scoreData.explanation || 'No explanation available'}
                            </p>
                          </div>

                          {/* Score Details */}
                          <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                            <span>Weighted Score: {scoreData.weighted_score.toFixed(2)}</span>
                            {scoreData.aggregation_used && (
                              <span>Aggregation: {scoreData.aggregation_type}</span>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    </motion.div>
                  )
                })}

                {/* Show More/Less Button */}
                {shouldCollapse && (
                  <div className="flex justify-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowAllQuestions(!showAllQuestions)}
                      className="gap-2"
                    >
                      {showAllQuestions ? (
                        <>
                          <ChevronUp className="h-4 w-4" />
                          Show Less ({questions.length - 5} hidden)
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-4 w-4" />
                          Show All Questions ({questions.length - 5} more)
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </>
            )
          })()}
            </CardContent>
          </Card>

          {/* Bonus Scores */}
          {finalThesisData.bonus_scores && Object.keys(finalThesisData.bonus_scores).length > 0 && (
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  Bonus Points
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(finalThesisData.bonus_scores).map(([ruleId, bonusData]) => (
                  <div key={ruleId} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <span className="text-sm font-medium text-green-900">
                      {bonusData.explanation}
                    </span>
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      +{bonusData.bonus_points} pts
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </motion.div>
      )}
    </div>
  )
}
